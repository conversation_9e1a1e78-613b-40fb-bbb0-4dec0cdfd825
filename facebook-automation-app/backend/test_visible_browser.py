#!/usr/bin/env python3
"""
Test visible antidetect browser functionality
"""
import asyncio
import aiohttp
import json
import time

BASE_URL = "http://localhost:8000"

async def test_visible_browser():
    """Test that antidetect browser opens visibly"""
    print("=== Testing Visible Antidetect Browser ===")
    
    profile_id = None
    session_id = None
    
    try:
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=120)) as session:
            # Step 1: Create profile
            print("1. Creating profile...")
            profile_data = {
                "name": f"Visible Browser Test {int(time.time())}",
                "description": "Test profile for visible browser",
                "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36",
                "screen_resolution": "1920x1080",
                "timezone": "UTC",
                "language": "en-US",
                "facebook_email": "<EMAIL>",
                "facebook_password": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@99",
                "facebook_username": "tranduy8683",
                "proxy_type": "no_proxy"
            }
            
            async with session.post(f"{BASE_URL}/api/profiles/", json=profile_data) as response:
                if response.status == 201:
                    profile = await response.json()
                    profile_id = profile['id']
                    print(f"✓ Profile created: {profile['name']} (ID: {profile_id})")
                else:
                    error = await response.text()
                    print(f"✗ Profile creation failed: {response.status} - {error}")
                    return False
            
            # Step 2: Create scraping session
            print("2. Creating scraping session...")
            scraping_data = {
                "name": f"Visible Browser Test Session {int(time.time())}",
                "post_url": "https://www.facebook.com/groups/591054007361950/posts/1234567890/",
                "profile_id": profile_id,
                "scraping_types": ["comments"],
                "include_comments": True,
                "include_likes": False,
                "include_shares": False,
                "include_reactions": False
            }
            
            async with session.post(f"{BASE_URL}/api/scraping/sessions", json=scraping_data) as response:
                if response.status == 201:
                    scraping_session = await response.json()
                    session_id = scraping_session['id']
                    print(f"✓ Scraping session created: {scraping_session['name']} (ID: {session_id})")
                else:
                    error = await response.text()
                    print(f"✗ Scraping session creation failed: {response.status} - {error}")
                    return False
            
            # Step 3: Start scraping and monitor for browser visibility
            print("3. Starting scraping...")
            print("🔍 WATCH YOUR SCREEN - Antidetect browser should open now!")
            print("Expected: Chrome window with Facebook page should appear")
            
            async with session.post(f"{BASE_URL}/api/scraping/sessions/{session_id}/start") as response:
                result = await response.json()
                
                print(f"Scraping start response status: {response.status}")
                print(f"Scraping start result:")
                print(json.dumps(result, indent=2))
                
                if response.status == 200:
                    print("✓ Scraping started successfully!")
                    
                    # Monitor progress and look for browser activity
                    await monitor_browser_activity(session, session_id)
                    
                    return True
                else:
                    print(f"✗ Scraping start failed: {response.status}")
                    return False
                    
    except Exception as e:
        print(f"✗ Test error: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # Keep browser open for manual inspection
        print("\n🔍 BROWSER INSPECTION:")
        print("If you can see a Chrome browser window with Facebook:")
        print("- The antidetect browser is working correctly")
        print("- You should see Facebook loaded in the browser")
        print("- The browser should have a custom user agent and profile")
        
        user_input = input("\nDid you see the antidetect browser window? (y/n): ")
        
        if user_input.lower() == 'y':
            print("✅ SUCCESS: Antidetect browser is visible and working!")
        else:
            print("❌ ISSUE: Browser not visible - need to investigate")
        
        # Cleanup
        if profile_id or session_id:
            await cleanup_test_data(profile_id, session_id)

async def monitor_browser_activity(session, session_id):
    """Monitor scraping progress and browser activity"""
    print("\n4. Monitoring browser activity...")
    
    for i in range(15):  # Monitor for up to 15 iterations (75 seconds)
        try:
            async with session.get(f"{BASE_URL}/api/scraping/sessions/{session_id}/progress") as response:
                if response.status == 200:
                    progress = await response.json()
                    
                    status = progress.get("status", "unknown")
                    percentage = progress.get("progress_percentage", 0)
                    step = progress.get("current_step", "Unknown")
                    
                    print(f"Progress {i+1}: {status} - {percentage}% - {step}")
                    
                    if "browser" in step.lower():
                        print("🌐 BROWSER ACTIVITY DETECTED!")
                        print("👀 Check your screen for Chrome browser window!")
                        
                    if "facebook" in step.lower():
                        print("📘 FACEBOOK ACTIVITY DETECTED!")
                        print("👀 Browser should be showing Facebook page!")
                        
                    if status in ["completed", "failed"]:
                        print(f"🏁 Scraping {status}")
                        break
                        
                else:
                    print(f"Failed to get progress: {response.status}")
                    
        except Exception as e:
            print(f"Error monitoring progress: {e}")
        
        await asyncio.sleep(5)  # Wait 5 seconds between checks

async def cleanup_test_data(profile_id, session_id):
    """Clean up test data"""
    print("\n5. Cleaning up test data...")
    
    async with aiohttp.ClientSession() as session:
        try:
            # Delete scraping session if exists
            if session_id:
                async with session.delete(f"{BASE_URL}/api/scraping/sessions/{session_id}") as response:
                    if response.status == 200:
                        print("✓ Scraping session deleted")
                    else:
                        print(f"⚠ Failed to delete scraping session: {response.status}")
            
            # Delete profile
            if profile_id:
                async with session.delete(f"{BASE_URL}/api/profiles/{profile_id}") as response:
                    if response.status == 200:
                        print("✓ Profile deleted")
                    else:
                        print(f"⚠ Failed to delete profile: {response.status}")
                        
        except Exception as e:
            print(f"⚠ Cleanup error: {e}")

async def main():
    """Main test function"""
    print("=== Visible Antidetect Browser Test ===")
    print("This test will verify that the antidetect browser opens visibly")
    print("Make sure the server is running on http://localhost:8000")
    print("IMPORTANT: Watch your screen for a Chrome browser window!\n")
    
    # Test server connectivity
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{BASE_URL}/docs") as response:
                if response.status == 200:
                    print("✓ Server is running and accessible")
                else:
                    print(f"✗ Server responded with status: {response.status}")
                    return
    except Exception as e:
        print(f"✗ Cannot connect to server: {e}")
        print("Please start the server with: python3 main.py")
        return
    
    # Test visible browser
    success = await test_visible_browser()
    
    print("\n" + "="*50)
    if success:
        print("✅ VISIBLE BROWSER TEST COMPLETED")
        print("Expected behavior:")
        print("1. Profile created successfully")
        print("2. Scraping session created successfully") 
        print("3. Antidetect browser window opened visibly")
        print("4. Facebook page loaded in browser")
        print("5. Scraping workflow executed")
    else:
        print("❌ VISIBLE BROWSER TEST FAILED")
        print("Check the errors above and server logs")
    
    print("="*50)

if __name__ == "__main__":
    asyncio.run(main())
