#!/usr/bin/env python3
"""
Test scraping workflow with antidetect browser
"""
import asyncio
import aiohttp
import json
import time

BASE_URL = "http://localhost:8000"

async def test_complete_scraping_workflow():
    """Test complete scraping workflow from profile creation to scraping"""
    print("=== Testing Complete Scraping Workflow ===")
    
    profile_id = None
    session_id = None
    
    try:
        async with aiohttp.ClientSession() as session:
            # Step 1: Create profile
            print("1. Creating profile...")
            profile_data = {
                "name": f"Scraping Test Profile {int(time.time())}",
                "description": "Test profile for scraping workflow",
                "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36",
                "screen_resolution": "1920x1080",
                "timezone": "UTC",
                "language": "en-US",
                "facebook_email": "<EMAIL>",
                "facebook_password": "<PERSON><PERSON><PERSON>uV<PERSON>@99",
                "facebook_username": "tranduy8683",
                "proxy_type": "no_proxy"
            }
            
            async with session.post(f"{BASE_URL}/api/profiles/", json=profile_data) as response:
                if response.status == 201:
                    profile = await response.json()
                    profile_id = profile['id']
                    print(f"✓ Profile created: {profile['name']} (ID: {profile_id})")
                else:
                    error = await response.text()
                    print(f"✗ Profile creation failed: {response.status} - {error}")
                    return False
            
            # Step 2: Facebook login
            print("2. Initiating Facebook login...")
            async with session.post(f"{BASE_URL}/api/profiles/{profile_id}/facebook-login") as response:
                result = await response.json()
                
                if response.status == 200 and result.get("status") == "browser_launched":
                    print("✓ Facebook login browser launched")
                    print("Please complete Facebook login manually in the browser window...")
                    
                    # Wait for user to complete login
                    input("Press Enter after completing Facebook login...")
                else:
                    print(f"⚠ Facebook login result: {result}")
            
            # Step 3: Create scraping session
            print("3. Creating scraping session...")
            scraping_data = {
                "name": f"Test Scraping Session {int(time.time())}",
                "post_url": "https://www.facebook.com/groups/591054007361950/posts/1234567890/",
                "profile_id": profile_id,
                "scraping_types": ["comments"],
                "include_comments": True,
                "include_likes": False,
                "include_shares": False,
                "include_reactions": False
            }
            
            async with session.post(f"{BASE_URL}/api/scraping/sessions", json=scraping_data) as response:
                if response.status == 201:
                    scraping_session = await response.json()
                    session_id = scraping_session['id']
                    print(f"✓ Scraping session created: {scraping_session['name']} (ID: {session_id})")
                else:
                    error = await response.text()
                    print(f"✗ Scraping session creation failed: {response.status} - {error}")
                    return False
            
            # Step 4: Start scraping
            print("4. Starting scraping with antidetect browser...")
            async with session.post(f"{BASE_URL}/api/scraping/sessions/{session_id}/start") as response:
                result = await response.json()
                
                print(f"Scraping start result: {json.dumps(result, indent=2)}")
                
                if response.status == 200:
                    print("✓ Scraping started successfully!")
                    print(f"Status: {result.get('status')}")
                    print(f"Profile: {result.get('profile_name')}")
                    print(f"Step: {result.get('step')}")
                    
                    # Monitor progress
                    await monitor_scraping_progress(session, session_id)
                    
                    return True
                else:
                    print(f"✗ Scraping start failed: {response.status}")
                    print(f"Error: {result}")
                    return False
                    
    except Exception as e:
        print(f"✗ Test error: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # Cleanup
        if profile_id:
            await cleanup_test_data(profile_id, session_id)

async def monitor_scraping_progress(session, session_id):
    """Monitor scraping progress"""
    print("\n5. Monitoring scraping progress...")
    
    for i in range(10):  # Monitor for up to 10 iterations
        try:
            async with session.get(f"{BASE_URL}/api/scraping/sessions/{session_id}/progress") as response:
                if response.status == 200:
                    progress = await response.json()
                    
                    status = progress.get("status", "unknown")
                    percentage = progress.get("progress_percentage", 0)
                    step = progress.get("current_step", "Unknown")
                    
                    print(f"Progress: {status} - {percentage}% - {step}")
                    
                    if status in ["completed", "failed"]:
                        break
                        
                    if status == "running" and "browser" in step.lower():
                        print("🌐 Antidetect browser should be visible now!")
                        
                else:
                    print(f"Failed to get progress: {response.status}")
                    
        except Exception as e:
            print(f"Error monitoring progress: {e}")
        
        await asyncio.sleep(5)  # Wait 5 seconds between checks

async def cleanup_test_data(profile_id, session_id):
    """Clean up test data"""
    print("\n6. Cleaning up test data...")
    
    async with aiohttp.ClientSession() as session:
        try:
            # Delete scraping session if exists
            if session_id:
                async with session.delete(f"{BASE_URL}/api/scraping/sessions/{session_id}") as response:
                    if response.status == 200:
                        print("✓ Scraping session deleted")
                    else:
                        print(f"⚠ Failed to delete scraping session: {response.status}")
            
            # Delete profile
            if profile_id:
                async with session.delete(f"{BASE_URL}/api/profiles/{profile_id}") as response:
                    if response.status == 200:
                        print("✓ Profile deleted")
                    else:
                        print(f"⚠ Failed to delete profile: {response.status}")
                        
        except Exception as e:
            print(f"⚠ Cleanup error: {e}")

async def test_server_connectivity():
    """Test if server is running"""
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{BASE_URL}/docs") as response:
                if response.status == 200:
                    print("✓ Server is running and accessible")
                    return True
                else:
                    print(f"✗ Server responded with status: {response.status}")
                    return False
    except Exception as e:
        print(f"✗ Cannot connect to server: {e}")
        return False

async def main():
    """Main test function"""
    print("=== Scraping Workflow Test ===")
    print("This will test the complete scraping workflow including antidetect browser")
    print("Make sure the server is running on http://localhost:8000\n")
    
    # Test server connectivity
    if not await test_server_connectivity():
        print("\nPlease start the server with: python3 main.py")
        return
    
    # Test complete workflow
    success = await test_complete_scraping_workflow()
    
    if success:
        print(f"\n✓ Scraping workflow test completed successfully!")
        print("You should have seen:")
        print("1. Profile created")
        print("2. Facebook login browser opened")
        print("3. Scraping session created")
        print("4. Antidetect browser opened for scraping")
        print("5. Progress monitoring")
    else:
        print("\n✗ Scraping workflow test failed. Check the errors above.")
    
    print("\nTest completed.")

if __name__ == "__main__":
    asyncio.run(main())
