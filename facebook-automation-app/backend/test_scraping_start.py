#!/usr/bin/env python3
"""
Simple test for scraping start functionality
"""
import asyncio
import aiohttp
import json
import time

BASE_URL = "http://localhost:8000"

async def test_scraping_start():
    """Test scraping start with existing profile"""
    print("=== Testing Scraping Start ===")
    
    profile_id = None
    session_id = None
    
    try:
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=60)) as session:
            # Step 1: Create profile
            print("1. Creating profile...")
            profile_data = {
                "name": f"Scraping Start Test {int(time.time())}",
                "description": "Test profile for scraping start",
                "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36",
                "screen_resolution": "1920x1080",
                "timezone": "UTC",
                "language": "en-US",
                "facebook_email": "<EMAIL>",
                "facebook_password": "DuyyeuVi@99",
                "facebook_username": "tranduy8683",
                "proxy_type": "no_proxy"
            }
            
            async with session.post(f"{BASE_URL}/api/profiles/", json=profile_data) as response:
                if response.status == 201:
                    profile = await response.json()
                    profile_id = profile['id']
                    print(f"✓ Profile created: {profile['name']} (ID: {profile_id})")
                else:
                    error = await response.text()
                    print(f"✗ Profile creation failed: {response.status} - {error}")
                    return False
            
            # Step 2: Create scraping session
            print("2. Creating scraping session...")
            scraping_data = {
                "name": f"Test Scraping Session {int(time.time())}",
                "post_url": "https://www.facebook.com/groups/591054007361950/posts/1234567890/",
                "profile_id": profile_id,
                "scraping_types": ["comments"],
                "include_comments": True,
                "include_likes": False,
                "include_shares": False,
                "include_reactions": False
            }
            
            async with session.post(f"{BASE_URL}/api/scraping/sessions", json=scraping_data) as response:
                if response.status == 201:
                    scraping_session = await response.json()
                    session_id = scraping_session['id']
                    print(f"✓ Scraping session created: {scraping_session['name']} (ID: {session_id})")
                else:
                    error = await response.text()
                    print(f"✗ Scraping session creation failed: {response.status} - {error}")
                    return False
            
            # Step 3: Start scraping
            print("3. Starting scraping...")
            print("This should open antidetect browser for scraping...")
            
            async with session.post(f"{BASE_URL}/api/scraping/sessions/{session_id}/start") as response:
                result = await response.json()
                
                print(f"Scraping start response status: {response.status}")
                print(f"Scraping start result:")
                print(json.dumps(result, indent=2))
                
                if response.status == 200:
                    print("✓ Scraping started successfully!")
                    print(f"Status: {result.get('status')}")
                    print(f"Profile: {result.get('profile_name')}")
                    print(f"Step: {result.get('step')}")
                    
                    # Check if browser should be opening
                    if "browser" in result.get('step', '').lower():
                        print("🌐 Antidetect browser should be opening now!")
                        print("Look for a Chrome window with Facebook page...")
                    
                    # Monitor progress briefly
                    await monitor_progress_briefly(session, session_id)
                    
                    return True
                else:
                    print(f"✗ Scraping start failed: {response.status}")
                    return False
                    
    except Exception as e:
        print(f"✗ Test error: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # Cleanup
        if profile_id or session_id:
            await cleanup_test_data(profile_id, session_id)

async def monitor_progress_briefly(session, session_id):
    """Monitor scraping progress briefly"""
    print("\n4. Monitoring progress briefly...")
    
    for i in range(3):  # Monitor for 3 iterations only
        try:
            async with session.get(f"{BASE_URL}/api/scraping/sessions/{session_id}/progress") as response:
                if response.status == 200:
                    progress = await response.json()
                    
                    status = progress.get("status", "unknown")
                    percentage = progress.get("progress_percentage", 0)
                    step = progress.get("current_step", "Unknown")
                    
                    print(f"Progress {i+1}: {status} - {percentage}% - {step}")
                    
                    if "browser" in step.lower():
                        print("🌐 Browser activity detected!")
                        
                else:
                    print(f"Failed to get progress: {response.status}")
                    
        except Exception as e:
            print(f"Error monitoring progress: {e}")
        
        await asyncio.sleep(3)  # Wait 3 seconds between checks

async def cleanup_test_data(profile_id, session_id):
    """Clean up test data"""
    print("\n5. Cleaning up test data...")
    
    async with aiohttp.ClientSession() as session:
        try:
            # Delete scraping session if exists
            if session_id:
                async with session.delete(f"{BASE_URL}/api/scraping/sessions/{session_id}") as response:
                    if response.status == 200:
                        print("✓ Scraping session deleted")
                    else:
                        print(f"⚠ Failed to delete scraping session: {response.status}")
            
            # Delete profile
            if profile_id:
                async with session.delete(f"{BASE_URL}/api/profiles/{profile_id}") as response:
                    if response.status == 200:
                        print("✓ Profile deleted")
                    else:
                        print(f"⚠ Failed to delete profile: {response.status}")
                        
        except Exception as e:
            print(f"⚠ Cleanup error: {e}")

async def main():
    """Main test function"""
    print("=== Scraping Start Test ===")
    print("This will test if antidetect browser opens when starting scraping")
    print("Make sure the server is running on http://localhost:8000\n")
    
    # Test server connectivity
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{BASE_URL}/docs") as response:
                if response.status == 200:
                    print("✓ Server is running and accessible")
                else:
                    print(f"✗ Server responded with status: {response.status}")
                    return
    except Exception as e:
        print(f"✗ Cannot connect to server: {e}")
        print("Please start the server with: python3 main.py")
        return
    
    # Test scraping start
    success = await test_scraping_start()
    
    if success:
        print(f"\n✓ Scraping start test completed!")
        print("Expected behavior:")
        print("1. Profile created successfully")
        print("2. Scraping session created successfully")
        print("3. Scraping started with 'pending' status")
        print("4. Antidetect browser should open for scraping")
        print("5. Progress should show browser activity")
    else:
        print("\n✗ Scraping start test failed. Check the errors above.")
    
    print("\nTest completed.")

if __name__ == "__main__":
    asyncio.run(main())
