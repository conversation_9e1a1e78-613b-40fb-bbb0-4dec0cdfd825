"""
Integrated Facebook Scraper Service with complete workflow
Combines all components: <PERSON><PERSON>er Manager, <PERSON> Handler, UID Extractor, Smart Scroller, Dynamic Loader, Deduplication
"""

import asyncio
import time
from typing import Dict, Any, List, Optional, Set, Callable
from loguru import logger
from pathlib import Path

from .browser_manager import <PERSON>rowser<PERSON>anager
from .facebook_session import <PERSON><PERSON>ession<PERSON>and<PERSON>
from .uid_extractor import Facebook<PERSON>DExtractor
from .smart_scroller import SmartScrollingEngine
from .dynamic_loader import DynamicContentLoader
from .deduplication_system import UIDDeduplicationSystem
from .parallel_processor import <PERSON>llelProcessor, ProcessingTask, ProcessingMode
from .error_handler import <PERSON>ust<PERSON><PERSON>r<PERSON>and<PERSON>, RetryConfig, ErrorType
from .memory_optimizer import MemoryOptimizer, StreamingProcessor
from .performance_monitor import PerformanceMonitor


class FacebookScraperService:
    """Complete Facebook scraping service with all advanced features"""
    
    def __init__(self, persistence_dir: str = "scraping_data"):
        # Initialize all components
        self.browser_manager = BrowserManager()
        self.session_handler = FacebookSessionHandler(self.browser_manager)
        self.uid_extractor = FacebookUIDExtractor()
        self.smart_scroller = SmartScrollingEngine()
        self.dynamic_loader = DynamicContentLoader()
        
        # Setup persistence
        self.persistence_dir = Path(persistence_dir)
        self.persistence_dir.mkdir(exist_ok=True)
        
        # Initialize deduplication system with persistence
        dedup_file = self.persistence_dir / "uid_deduplication.json"
        self.deduplication_system = UIDDeduplicationSystem(
            max_memory_uids=50000,
            persistence_file=str(dedup_file)
        )

        # Initialize parallel processor
        self.parallel_processor = ParallelProcessor(self)

        # Initialize error handler with custom retry configuration
        retry_config = RetryConfig(
            max_attempts=3,
            base_delay=2.0,
            max_delay=120.0,
            backoff_multiplier=2.0,
            jitter=True
        )
        self.error_handler = RobustErrorHandler(retry_config)

        # Initialize memory optimizer
        self.memory_optimizer = MemoryOptimizer()
        self.streaming_processor = StreamingProcessor()

        # Initialize performance monitor
        self.performance_monitor = PerformanceMonitor(collection_interval=30.0)
        self.profiler = self.performance_monitor.profiler

        # Setup anomaly detection callback
        self.performance_monitor.add_anomaly_callback(self._handle_performance_anomaly)

        # Performance monitoring will be started when needed
        self._monitoring_started = False
        
        # Scraping statistics
        self.scraping_stats = {
            "sessions_created": 0,
            "pages_scraped": 0,
            "total_uids_extracted": 0,
            "unique_uids_found": 0,
            "total_scraping_time": 0,
            "start_time": None
        }

    async def _ensure_monitoring_started(self):
        """Ensure performance monitoring is started"""
        if not self._monitoring_started:
            await self.performance_monitor.start_monitoring()
            self._monitoring_started = True

    async def scrape_facebook_post_uids(
        self,
        profile_id: str,
        post_url: str,
        max_scroll_time: int = 300,
        progress_callback: Optional[Callable] = None
    ) -> Dict[str, Any]:
        """
        Complete workflow to scrape UIDs from Facebook post with robust error handling
        """
        return await self.error_handler.execute_with_retry(
            self._scrape_facebook_post_uids_internal,
            (profile_id, post_url, max_scroll_time, progress_callback),
            {},
            recovery_enabled=True
        )

    async def _scrape_facebook_post_uids_internal(
        self,
        profile_id: str,
        post_url: str,
        max_scroll_time: int = 300,
        progress_callback: Optional[Callable] = None
    ) -> Dict[str, Any]:
        """
        Internal implementation of Facebook post UID scraping

        Args:
            profile_id: Browser profile ID to use
            post_url: Facebook post URL to scrape
            max_scroll_time: Maximum time to spend scrolling (seconds)
            progress_callback: Function to report progress
        """
        # Ensure monitoring is started
        await self._ensure_monitoring_started()

        # Start performance profiling
        start_time = time.time()
        success = True
        error_msg = None

        try:
            logger.info(f"Starting Facebook UID scraping for post: {post_url}")
            start_time = time.time()

            # Step 1: Initialize session
            session_result = await self._ensure_facebook_session(profile_id)
            if not session_result["success"]:
                return session_result

            # Step 2: Navigate to post
            browser = await self.browser_manager.get_browser(profile_id)
            if not browser:
                return {"success": False, "error": "Browser not available"}

            page = await browser.get(post_url)
            await asyncio.sleep(3)

            # Step 3: Load all dynamic content
            logger.info("Loading dynamic content...")
            dynamic_result = await self.dynamic_loader.load_all_dynamic_content(
                browser,
                max_iterations=30,
                progress_callback=self._create_dynamic_progress_callback(progress_callback)
            )

            # Step 4: Smart scrolling to load all comments
            logger.info("Starting smart scrolling...")
            scroll_result = await self.smart_scroller.scroll_with_comment_loading(
                browser=browser,
                page_url=post_url,
                uid_extractor=self.uid_extractor
            )

            # Step 5: Extract all UIDs
            logger.info("Extracting UIDs...")
            uids = await self._extract_all_uids_from_page(browser)

            # Step 6: Deduplicate UIDs
            logger.info("Deduplicating UIDs...")
            new_uids, duplicate_uids = self.deduplication_system.add_uids_batch(list(uids))

            # Step 7: Save results
            await self._save_scraping_results(profile_id, post_url, new_uids, {
                "dynamic_loading": dynamic_result,
                "scrolling": scroll_result,
                "total_uids": len(uids),
                "new_uids": len(new_uids),
                "duplicates": len(duplicate_uids)
            })

            # Update statistics
            scraping_time = time.time() - start_time
            self.scraping_stats["pages_scraped"] += 1
            self.scraping_stats["total_uids_extracted"] += len(uids)
            self.scraping_stats["unique_uids_found"] += len(new_uids)
            self.scraping_stats["total_scraping_time"] += scraping_time

            logger.info(f"Scraping completed: {len(new_uids)} new UIDs found in {scraping_time:.1f}s")

            return {
                "success": True,
                "results": {
                    "post_url": post_url,
                    "total_uids_found": len(uids),
                    "new_uids": new_uids,
                    "duplicate_count": len(duplicate_uids),
                    "scraping_time": scraping_time,
                    "dynamic_loading_stats": dynamic_result.get("stats", {}),
                    "scrolling_stats": scroll_result.get("stats", {}),
                    "deduplication_stats": self.deduplication_system.get_statistics()
                }
            }

        except Exception as e:
            success = False
            error_msg = str(e)
            logger.error(f"Error scraping Facebook post: {e}")
            return {"success": False, "error": str(e)}

        finally:
            # Record performance metrics
            end_time = time.time()
            duration = end_time - start_time

            # Log performance data
            if hasattr(self, 'performance_monitor') and hasattr(self.performance_monitor, 'metrics_collector'):
                try:
                    self.performance_monitor.metrics_collector.record_operation(
                        operation_name="scrape_facebook_post_uids",
                        duration=duration,
                        success=success
                    )
                except Exception as perf_error:
                    logger.warning(f"Failed to record performance metrics: {perf_error}")
    
    async def scrape_multiple_posts(
        self,
        profile_id: str,
        post_urls: List[str],
        progress_callback: Optional[Callable] = None
    ) -> Dict[str, Any]:
        """Scrape multiple Facebook posts in sequence"""
        try:
            results = []
            total_new_uids = []
            
            for i, post_url in enumerate(post_urls):
                logger.info(f"Scraping post {i+1}/{len(post_urls)}: {post_url}")
                
                result = await self.scrape_facebook_post_uids(
                    profile_id=profile_id,
                    post_url=post_url,
                    progress_callback=progress_callback
                )
                
                results.append(result)
                
                if result["success"]:
                    total_new_uids.extend(result["results"]["new_uids"])
                
                # Small delay between posts
                await asyncio.sleep(2)
            
            return {
                "success": True,
                "results": {
                    "posts_scraped": len(post_urls),
                    "successful_scrapes": len([r for r in results if r["success"]]),
                    "total_unique_uids": len(set(total_new_uids)),
                    "individual_results": results,
                    "overall_stats": self.get_scraping_statistics()
                }
            }
            
        except Exception as e:
            logger.error(f"Error scraping multiple posts: {e}")
            return {"success": False, "error": str(e)}

    async def scrape_multiple_posts_parallel(
        self,
        profile_id: str,
        post_urls: List[str],
        max_concurrent: Optional[int] = None,
        progress_callback: Optional[Callable] = None
    ) -> Dict[str, Any]:
        """Scrape multiple Facebook posts in parallel for maximum performance"""
        try:
            logger.info(f"Starting parallel scraping for {len(post_urls)} posts")

            # Create processing tasks
            tasks = []
            for i, post_url in enumerate(post_urls):
                task = ProcessingTask(
                    task_id=f"parallel_task_{int(time.time())}_{i}",
                    profile_id=profile_id,
                    post_url=post_url,
                    priority=1,
                    max_scroll_time=300
                )
                tasks.append(task)

            # Submit tasks to parallel processor
            task_ids = await self.parallel_processor.submit_multiple_tasks(tasks)

            # Process tasks in parallel
            results = await self.parallel_processor.process_tasks_parallel(
                max_concurrent=max_concurrent,
                progress_callback=progress_callback
            )

            # Aggregate results
            total_new_uids = []
            successful_scrapes = 0
            failed_scrapes = 0

            for result in results:
                if result.success:
                    successful_scrapes += 1
                    if result.uids:
                        total_new_uids.extend(result.uids)
                else:
                    failed_scrapes += 1

            # Remove duplicates across all results
            unique_uids = list(set(total_new_uids))

            return {
                "success": True,
                "results": {
                    "posts_scraped": len(post_urls),
                    "successful_scrapes": successful_scrapes,
                    "failed_scrapes": failed_scrapes,
                    "total_unique_uids": len(unique_uids),
                    "all_unique_uids": unique_uids,
                    "individual_results": [
                        {
                            "task_id": r.task_id,
                            "success": r.success,
                            "uids_count": len(r.uids) if r.uids else 0,
                            "processing_time": r.processing_time,
                            "error": r.error
                        } for r in results
                    ],
                    "parallel_processing_stats": self.parallel_processor.get_processing_statistics(),
                    "overall_stats": self.get_scraping_statistics()
                }
            }

        except Exception as e:
            logger.error(f"Error in parallel scraping: {e}")
            return {"success": False, "error": str(e)}

    async def scrape_with_auto_optimization(
        self,
        profile_id: str,
        post_urls: List[str],
        progress_callback: Optional[Callable] = None
    ) -> Dict[str, Any]:
        """Automatically optimize scraping strategy based on system resources and post count"""
        try:
            post_count = len(post_urls)

            # Auto-scale workers based on current system resources
            await self.parallel_processor.auto_scale_workers()

            # Choose optimal processing strategy
            if post_count == 1:
                # Single post - use regular scraping
                return await self.scrape_facebook_post_uids(
                    profile_id=profile_id,
                    post_url=post_urls[0],
                    progress_callback=progress_callback
                )
            elif post_count <= 5:
                # Small batch - use concurrent processing
                return await self.scrape_multiple_posts(
                    profile_id=profile_id,
                    post_urls=post_urls,
                    progress_callback=progress_callback
                )
            else:
                # Large batch - use parallel processing
                optimal_concurrency = self.parallel_processor.resource_monitor.get_optimal_concurrency()
                return await self.scrape_multiple_posts_parallel(
                    profile_id=profile_id,
                    post_urls=post_urls,
                    max_concurrent=optimal_concurrency,
                    progress_callback=progress_callback
                )

        except Exception as e:
            logger.error(f"Error in auto-optimized scraping: {e}")
            return {"success": False, "error": str(e)}

    async def scrape_with_memory_optimization(
        self,
        profile_id: str,
        post_urls: List[str],
        progress_callback: Optional[Callable] = None
    ) -> Dict[str, Any]:
        """Scrape with advanced memory optimization and streaming processing"""
        try:
            async with self.memory_optimizer.memory_managed_operation(
                f"scrape_memory_optimized_{len(post_urls)}_posts"
            ):
                logger.info(f"Starting memory-optimized scraping for {len(post_urls)} posts")

                # Use streaming processor for large datasets
                if len(post_urls) > 10:
                    return await self._stream_process_posts(
                        profile_id, post_urls, progress_callback
                    )
                else:
                    # Use regular parallel processing for smaller datasets
                    return await self.scrape_with_auto_optimization(
                        profile_id, post_urls, progress_callback
                    )

        except Exception as e:
            logger.error(f"Error in memory-optimized scraping: {e}")
            return {"success": False, "error": str(e)}

    async def _stream_process_posts(
        self,
        profile_id: str,
        post_urls: List[str],
        progress_callback: Optional[Callable] = None
    ) -> Dict[str, Any]:
        """Stream process posts for memory efficiency"""
        try:
            all_uids = []
            processed_posts = 0
            successful_scrapes = 0
            failed_scrapes = 0

            async def uid_generator():
                """Generate UIDs from posts in streaming fashion"""
                for post_url in post_urls:
                    try:
                        result = await self._scrape_facebook_post_uids_internal(
                            profile_id, post_url, 300, None
                        )

                        if result["success"] and result["results"]["new_uids"]:
                            for uid in result["results"]["new_uids"]:
                                yield uid

                        nonlocal processed_posts, successful_scrapes, failed_scrapes
                        processed_posts += 1
                        if result["success"]:
                            successful_scrapes += 1
                        else:
                            failed_scrapes += 1

                        if progress_callback:
                            await progress_callback({
                                "processed_posts": processed_posts,
                                "total_posts": len(post_urls),
                                "successful_scrapes": successful_scrapes,
                                "failed_scrapes": failed_scrapes
                            })

                    except Exception as e:
                        logger.error(f"Error processing post {post_url}: {e}")
                        failed_scrapes += 1

            async def process_uid_chunk(uid_chunk: List[str]) -> List[str]:
                """Process chunk of UIDs with deduplication"""
                return self.deduplication_system.add_uids_batch(uid_chunk)

            # Stream process UIDs
            async for processed_chunk in self.streaming_processor.process_uids_stream(
                uid_generator(), process_uid_chunk, progress_callback
            ):
                all_uids.extend(processed_chunk)

            return {
                "success": True,
                "results": {
                    "posts_scraped": len(post_urls),
                    "successful_scrapes": successful_scrapes,
                    "failed_scrapes": failed_scrapes,
                    "total_unique_uids": len(all_uids),
                    "all_unique_uids": all_uids,
                    "memory_stats": self.memory_optimizer.get_memory_statistics(),
                    "streaming_stats": {
                        "processed_count": self.streaming_processor.processed_count,
                        "chunk_size": self.streaming_processor.chunk_size
                    }
                }
            }

        except Exception as e:
            logger.error(f"Error in stream processing: {e}")
            return {"success": False, "error": str(e)}

    async def _handle_performance_anomaly(self, anomalies: List[Dict[str, Any]]):
        """Handle detected performance anomalies"""
        try:
            for anomaly in anomalies:
                logger.warning(f"Performance anomaly detected: {anomaly}")

                # Take corrective actions based on anomaly type
                if anomaly["type"] == "high_memory":
                    await self.memory_optimizer.optimize_memory_usage()
                elif anomaly["type"] == "high_cpu":
                    # Reduce concurrent operations
                    if hasattr(self, 'parallel_processor'):
                        await self.parallel_processor.auto_scale_workers()
                elif anomaly["type"] == "high_error_rate":
                    # Log error rate issue
                    logger.error(f"High error rate detected for operation: {anomaly.get('operation')}")
                elif anomaly["type"] == "slow_response":
                    # Log slow response
                    logger.warning(f"Slow response detected for: {anomaly.get('metric')}")

        except Exception as e:
            logger.error(f"Error handling performance anomaly: {e}")

    def get_performance_report(self) -> Dict[str, Any]:
        """Get comprehensive performance report"""
        try:
            return self.performance_monitor.get_comprehensive_report()
        except Exception as e:
            logger.error(f"Error getting performance report: {e}")
            return {"error": str(e)}

    async def _ensure_facebook_session(self, profile_id: str) -> Dict[str, Any]:
        """Ensure Facebook session is active with proper profile validation"""
        try:
            logger.info(f"Ensuring Facebook session for profile: {profile_id}")

            # First, validate that the profile has Facebook login information
            profile_validation = await self._validate_profile_for_scraping(profile_id)
            if not profile_validation["success"]:
                return profile_validation

            # Check if session already exists and is active
            if await self.session_handler.is_session_active(profile_id):
                logger.info(f"Using existing Facebook session for profile: {profile_id}")

                # Skip login verification for now - let scraper handle login issues
                logger.info("Skipping login verification - proceeding with existing session")
                return {"success": True, "message": "Session already active - proceeding without verification"}

            # Initiate new login with saved cookies
            login_result = await self.session_handler.initiate_login_with_cookies(profile_id)
            if not login_result["success"]:
                logger.error(f"Failed to initiate login with cookies: {login_result.get('error', 'Unknown error')}")
                return login_result

            # Wait for login completion or verification
            logger.info("Verifying Facebook login status...")
            max_wait = 60  # 1 minute for cookie-based login
            wait_time = 0

            while wait_time < max_wait:
                status = await self.session_handler.check_login_status(profile_id)

                if status["success"] and status["status"] == "logged_in":
                    complete_result = await self.session_handler.complete_login(profile_id)
                    self.scraping_stats["sessions_created"] += 1
                    logger.info(f"Facebook session established successfully for profile: {profile_id}")
                    return complete_result
                elif status["success"] and status["status"] == "login_required":
                    return {
                        "success": False,
                        "error": "Profile not logged in to Facebook. Please login first using the 'Facebook Login' button.",
                        "status": "login_required"
                    }

                await asyncio.sleep(3)
                wait_time += 3

            return {
                "success": False,
                "error": "Login verification timeout. Profile may not be properly logged in to Facebook.",
                "status": "timeout"
            }

        except Exception as e:
            logger.error(f"Error ensuring Facebook session: {e}")
            return {"success": False, "error": str(e)}

    async def _validate_profile_for_scraping(self, profile_id: str) -> Dict[str, Any]:
        """Validate that profile has necessary Facebook login information for scraping"""
        try:
            # This would typically check the database for profile information
            # For now, we'll assume the profile exists and check for cookies/session data
            logger.info(f"Validating profile {profile_id} for scraping")

            # Check if profile directory exists and has cookies
            from pathlib import Path

            # Try multiple possible profile directory locations
            possible_dirs = [
                Path(f"browser_profiles/{profile_id}"),
                Path(f"browser_sessions/profile_{profile_id}"),
                Path(f"browser_sessions/{profile_id}")
            ]

            profile_dir = None
            for dir_path in possible_dirs:
                if dir_path.exists():
                    profile_dir = dir_path
                    break

            if not profile_dir:
                return {
                    "success": False,
                    "error": f"Profile directory not found: {profile_id}. Please create the profile first.",
                    "status": "profile_not_found"
                }

            # Check for cookies in different possible locations
            cookies_locations = [
                profile_dir / "Default" / "Cookies",
                profile_dir / "Cookies",
                profile_dir / "Default" / "Network" / "Cookies"
            ]

            cookies_found = False
            for cookies_file in cookies_locations:
                if cookies_file.exists():
                    cookies_found = True
                    break

            if not cookies_found:
                logger.warning(f"No browser cookies found for profile: {profile_id}. Profile may need Facebook login.")
                # Don't fail validation - let the scraper attempt to work with the profile
                # return {
                #     "success": False,
                #     "error": f"No browser cookies found for profile: {profile_id}. Please login to Facebook first.",
                #     "status": "no_cookies"
                # }

            logger.info(f"Profile {profile_id} validation passed - using directory: {profile_dir}")
            return {"success": True, "message": "Profile validated for scraping", "profile_dir": str(profile_dir)}

        except Exception as e:
            logger.error(f"Error validating profile {profile_id}: {e}")
            return {"success": False, "error": str(e)}

    async def _verify_facebook_login_status(self, profile_id: str) -> Dict[str, Any]:
        """Verify if the current session is actually logged in to Facebook"""
        try:
            browser = await self.browser_manager.get_browser(profile_id)
            if not browser:
                return {"success": False, "logged_in": False, "message": "Browser not available"}

            # Navigate to Facebook and check login status
            page = browser.main_tab
            await page.get("https://www.facebook.com")
            await asyncio.sleep(3)

            # Check current URL and page content for login indicators
            current_url = await page.evaluate("window.location.href")
            page_title = await page.evaluate("document.title")

            # Simple check - if redirected to login page, not logged in
            if "login" in current_url.lower() or "login" in page_title.lower():
                return {
                    "success": True,
                    "logged_in": False,
                    "message": "Redirected to login page - not logged in"
                }

            # Additional check for Facebook-specific elements that indicate login
            try:
                # Look for user menu or profile elements
                user_menu = await page.evaluate("""
                    document.querySelector('[data-testid="blue_bar_profile_link"]') ||
                    document.querySelector('[aria-label*="Account"]') ||
                    document.querySelector('[role="button"][aria-label*="Account"]')
                """)

                if user_menu:
                    return {
                        "success": True,
                        "logged_in": True,
                        "message": "Successfully logged in to Facebook"
                    }
            except Exception as e:
                logger.warning(f"Error checking for user menu elements: {e}")

            return {
                "success": True,
                "logged_in": False,
                "message": "Could not verify login status"
            }

        except Exception as e:
            logger.error(f"Error verifying Facebook login status: {e}")
            return {"success": False, "logged_in": False, "message": str(e)}
    
    async def _extract_all_uids_from_page(self, browser) -> Set[str]:
        """Extract all UIDs from current page"""
        try:
            page = browser.main_tab
            if not page:
                return set()
            
            all_uids = set()
            
            # Extract from page source
            current_url = page.url
            page_uids = await self.uid_extractor.extract_uids_from_page(browser, current_url)
            all_uids.update(page_uids)
            
            # Extract specifically from comments section
            comment_uids = await self.uid_extractor.extract_uids_from_comments_section(page)
            all_uids.update(comment_uids)
            
            # Filter valid UIDs
            valid_uids = self.uid_extractor.filter_valid_uids(all_uids)
            
            logger.info(f"Extracted {len(valid_uids)} valid UIDs from page")
            return valid_uids
            
        except Exception as e:
            logger.error(f"Error extracting UIDs from page: {e}")
            return set()
    
    async def _save_scraping_results(
        self, 
        profile_id: str, 
        post_url: str, 
        uids: List[str], 
        metadata: Dict[str, Any]
    ):
        """Save scraping results to file"""
        try:
            # Create results file
            timestamp = int(time.time())
            filename = f"scraping_results_{profile_id}_{timestamp}.json"
            results_file = self.persistence_dir / filename
            
            data = {
                "profile_id": profile_id,
                "post_url": post_url,
                "timestamp": timestamp,
                "uids": uids,
                "metadata": metadata,
                "scraping_stats": self.scraping_stats
            }
            
            import json
            with open(results_file, 'w') as f:
                json.dump(data, f, indent=2)
            
            # Save deduplication state
            self.deduplication_system.save_to_file()
            
            logger.info(f"Results saved to {results_file}")
            
        except Exception as e:
            logger.error(f"Error saving results: {e}")
    
    def _create_dynamic_progress_callback(self, main_callback: Optional[Callable]) -> Callable:
        """Create progress callback for dynamic loading"""
        async def callback(stats):
            if main_callback:
                await main_callback({
                    "stage": "dynamic_loading",
                    "stats": stats
                })
        return callback
    
    def get_scraping_statistics(self) -> Dict[str, Any]:
        """Get comprehensive scraping statistics"""
        return {
            "scraping_stats": self.scraping_stats,
            "deduplication_stats": self.deduplication_system.get_statistics(),
            "browser_stats": {
                "active_browsers": len(self.browser_manager.browsers),
                "active_sessions": len(self.session_handler.sessions)
            },
            "error_stats": self.error_handler.get_error_statistics(),
            "parallel_processing_stats": self.parallel_processor.get_processing_statistics() if hasattr(self, 'parallel_processor') else {},
            "memory_stats": self.memory_optimizer.get_memory_statistics() if hasattr(self, 'memory_optimizer') else {},
            "performance_report": self.get_performance_report() if hasattr(self, 'performance_monitor') else {}
        }
    
    async def cleanup(self):
        """Cleanup all resources"""
        try:
            # Cleanup parallel processor
            await self.parallel_processor.cleanup()

            # Cleanup memory optimizer
            await self.memory_optimizer.cleanup()

            # Cleanup performance monitor
            await self.performance_monitor.cleanup()

            # Close all browser sessions
            await self.browser_manager.close_all_browsers()

            # Save final deduplication state
            self.deduplication_system.save_to_file()

            logger.info("Facebook scraper service cleanup completed")

        except Exception as e:
            logger.error(f"Error during cleanup: {e}")
    
    async def get_unique_uids_count(self) -> int:
        """Get total count of unique UIDs collected"""
        return len(self.deduplication_system.get_unique_uids())
    
    async def export_all_uids(self, filename: Optional[str] = None) -> str:
        """Export all unique UIDs to file"""
        try:
            if not filename:
                timestamp = int(time.time())
                filename = f"all_unique_uids_{timestamp}.txt"
            
            export_file = self.persistence_dir / filename
            unique_uids = self.deduplication_system.get_unique_uids()
            
            with open(export_file, 'w') as f:
                for uid in sorted(unique_uids):
                    f.write(f"{uid}\n")
            
            logger.info(f"Exported {len(unique_uids)} unique UIDs to {export_file}")
            return str(export_file)
            
        except Exception as e:
            logger.error(f"Error exporting UIDs: {e}")
            return ""
