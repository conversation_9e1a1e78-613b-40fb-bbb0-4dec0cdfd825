#!/usr/bin/env python3
"""
Direct test of browser manager to verify visible browser launch
"""
import asyncio
import sys
import os
from pathlib import Path

# Add the backend directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from automation.browser_manager import BrowserManager

async def test_direct_browser_launch():
    """Test direct browser launch to verify visibility"""
    print("=== Direct Browser Launch Test ===")
    print("This will test browser_manager directly to verify visible browser")
    
    browser_manager = BrowserManager()
    browser = None
    
    try:
        # Create test profile config
        profile_id = "test_visible_browser"
        
        # Create profile directory and config
        profiles_dir = Path("browser_sessions")
        profile_path = profiles_dir / f"profile_{profile_id}"
        profile_path.mkdir(parents=True, exist_ok=True)
        
        config_file = profile_path / "config.json"
        
        import json
        browser_config = {
            "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "screen_resolution": "1920x1080",
            "timezone": "UTC",
            "language": "en-US",
            "proxy": None,
            "profile_path": str(profile_path)
        }
        
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(browser_config, f, indent=2)
        
        print(f"✓ Created test profile config: {config_file}")
        
        # Launch browser
        print("🚀 Launching antidetect browser...")
        print("👀 WATCH YOUR SCREEN - Browser window should appear!")
        
        browser = await browser_manager.launch_browser(profile_id)
        
        print("✅ Browser launched successfully!")
        print(f"Browser object: {browser}")
        
        # Navigate to a test page
        print("🌐 Navigating to test page...")
        page = browser.main_tab
        await page.get("https://www.google.com")
        
        print("✅ Navigation completed!")
        print("🔍 You should see a Chrome browser window with Google page")
        
        # Keep browser open for inspection
        print("\n" + "="*50)
        print("BROWSER INSPECTION:")
        print("- Check if you can see a Chrome browser window")
        print("- The window should show Google homepage")
        print("- Browser should have antidetect features enabled")
        print("="*50)
        
        user_input = input("\nCan you see the browser window? (y/n): ")
        
        if user_input.lower() == 'y':
            print("✅ SUCCESS: Antidetect browser is visible and working!")
            
            # Test Facebook navigation
            print("\n🔄 Testing Facebook navigation...")
            await page.get("https://www.facebook.com")
            print("✅ Facebook navigation completed!")
            
            input("Press Enter to close browser...")
            
        else:
            print("❌ ISSUE: Browser not visible")
            print("Possible causes:")
            print("- Browser running in background")
            print("- Window minimized or hidden")
            print("- Display/focus issues")
            
        return user_input.lower() == 'y'
        
    except Exception as e:
        print(f"❌ Error during browser test: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # Cleanup
        if browser:
            try:
                print("🧹 Closing browser...")
                await browser.close()
                print("✅ Browser closed")
            except Exception as e:
                print(f"⚠ Error closing browser: {e}")

async def main():
    """Main test function"""
    print("=== Direct Browser Manager Test ===")
    print("This test will launch browser directly using BrowserManager")
    print("to verify that antidetect browser opens visibly\n")
    
    success = await test_direct_browser_launch()
    
    print("\n" + "="*50)
    if success:
        print("✅ DIRECT BROWSER TEST PASSED")
        print("The antidetect browser is working correctly!")
    else:
        print("❌ DIRECT BROWSER TEST FAILED")
        print("Need to investigate browser visibility issues")
    print("="*50)

if __name__ == "__main__":
    asyncio.run(main())
